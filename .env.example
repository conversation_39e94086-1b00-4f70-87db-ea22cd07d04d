# API Configuration
API_KEY=your_api_key_here
GEMMA_API_KEY=your_gemma_api_key_here

# Storage Configuration
LOCAL_STORAGE_PATH=/app/data/tmp
VOICE_FILES_PATH=/app/data/voices

# TTS Server Configuration
TTS_SERVER_URL=https://tts.dahopevi.com/api

# Optional: Cloud Storage Configuration (for S3-compatible storage like MinIO, DigitalOcean Spaces, AWS S3)
S3_ENDPOINT_URL= # e.g., https://s3.your-region.amazonaws.com or your MinIO endpoint
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_BUCKET_NAME=
S3_REGION= # e.g., us-east-1

# Simone Integration Specific Configuration
# Set to 'true' to upload Simone outputs (blog post, screenshots) to S3 if S3 credentials are provided.
# If 'false' or S3 credentials are not fully configured, outputs will be saved locally.
SIMONE_UPLOAD_TO_S3=false

# Optional: Google Cloud Storage Configuration
GCP_SA_CREDENTIALS=
GCP_BUCKET_NAME=
