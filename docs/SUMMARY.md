# DahoPevi Documentation

* [Welcome to DahoPevi](README.md)

## 🎥 Video Operations

### Basic Operations
* [Caption Video](video/caption_video.md)
* [Concatenate Videos](video/concatenate.md)
* [Cut Video](video/cut.md)
* [Split Video](video/split.md)
* [Thumbnail Generation](video/thumbnail.md)
* [Trim Video](video/trim.md)
* [Video to Blog Post (Simone)](simone/index.md)
* [Roadmap](video/roadmap.md)

## 🔊 Audio Operations
* [Audio Concatenation](audio/concatenate.md)
* [Speech Processing](audio/speech.md)

## 🖼️ Image Operations
* [Image to Video Conversion](image/convert/image_to_video.md)

## 📹 Media Operations
* [Media Cutting](media/cut.md)
* [Media Download](media/download.md)
* [Duration Analysis](media/duration.md)
* [Feedback Handling](media/feedback.md)
* [Media Transcription](media/media_transcribe.md)
* [Metadata Management](media/metadata.md)
* [Serve Local Files](media/serve_files.md)
* [Silence Detection](media/silence.md)

### Media Conversion
* [General Media Conversion](media/convert/media_convert.md)
* [Media to MP3](media/convert/media_to_mp3.md)

## 💻 Code Operations
* [Python Code Execution](code/execute/execute_python.md)

## 🔧 FFmpeg Tools
* [FFmpeg Composition](ffmpeg/ffmpeg_compose.md)

## ☁️ Cloud Storage
* [S3 Upload](s3/upload.md)

## 🚀 Cloud Installation
* [DigitalOcean Setup](cloud-installation/do.md)
* [Google Cloud Platform Setup](cloud-installation/gcp.md)

## 🛠️ Toolkit Essentials
* [Authentication](toolkit/authenticate.md)
* [Job Status](toolkit/job_status.md)
* [Jobs Status Overview](toolkit/jobs_status.md)
* [Testing](toolkit/test.md)
